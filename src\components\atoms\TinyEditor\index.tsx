import { styled } from "@linaria/react";
import { Editor } from "./hugerte/editor";
import { Button, Input } from "antd";
import i18next from "i18next";
import { Dialog } from "primereact/dialog";
import { useRef, useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";

interface Props {
  value: any;
  onEditorChange?: any;
  minimal?: boolean;
  noFocus?: boolean;
  disabled?: boolean;
}

type IframeType = {
  url: string;
  height: string;
  width: string;
};
const TinyEditor = ({
  value,
  onEditorChange,
  minimal,
  noFocus,
  disabled,
}: Props) => {
  const editorRef = useRef(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();

  const [iframe, setIframe] = useState(null as IframeType);
  const [showIframeModal, setShowIframeModal] = useState(false);
  const [editorHeight, setEditorHeight] = useState<number | string>("auto");
  const editorProperties = useSelector(
    (state: RootState) => state.globalSettings.editorProperties
  );

  // Calculate dynamic height based on parent container
  const calculateHeight = useCallback(() => {
    if (containerRef.current) {
      const container = containerRef.current;
      const parent = container.parentElement;

      if (parent) {
        const parentHeight = parent.clientHeight;
        const parentStyles = window.getComputedStyle(parent);
        const parentPadding =
          parseFloat(parentStyles.paddingTop) +
          parseFloat(parentStyles.paddingBottom);

        // Calculate available height, minimum 150px
        const availableHeight = Math.max(150, parentHeight - parentPadding - 20);

        // If parent has no defined height, use minimum height
        if (parentHeight === 0 || !parentHeight) {
          setEditorHeight(150);
        } else {
          setEditorHeight(availableHeight);
        }
      } else {
        setEditorHeight(150);
      }
    }
  }, []);

  // Set up resize observer for responsive height
  useEffect(() => {
    calculateHeight();

    const resizeObserver = new ResizeObserver(() => {
      calculateHeight();
    });

    if (containerRef.current?.parentElement) {
      resizeObserver.observe(containerRef.current.parentElement);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [calculateHeight]);

  const handleEditorInit = (_, editor) => {
    if (editor && !noFocus) {
      editorRef.current = editor;
      editorRef.current.focus();
    }
  };



  const getMenuBar = () => {
    return editorProperties?.menubar;
  };

  const getPlugins = () => {
    return minimal
      ? "preview charmap emoticons wordcount"
      : "preview charmap emoticons image link lists searchreplace table wordcount";
  };

  const getToolbar = () => {
    return minimal
      ? "undo redo | fontfamily fontsize align | bold italic underline strikethrough | emoticons"
      : editorProperties?.toolbar;
  };

  const generateURL = () => {
    if (!iframe?.url.startsWith("http")) {
      return `https://${iframe?.url}`;
    }

    return iframe?.url;
  };

  const insertIframe = () => {
    const iframeHTML = `<iframe src="${generateURL()}" width=${
      iframe?.width
    } height=${iframe?.height} frameborder="0" allowfullscreen></iframe>`;
    editorRef.current.insertContent(iframeHTML);
    setShowIframeModal(false);
  };

  return (
    <>
      <EditorContainer ref={containerRef} height={editorHeight}>
        <Editor
          value={value}
          onEditorChange={onEditorChange}
          disabled={disabled}
          init={{
            language: i18next.language.startsWith("pl") ? "pl" : "en",
            setup: (editor) => {
              editor.on("init", (evt) => handleEditorInit(evt, editor));
              editor.ui.registry.addButton("insertIframe", {
                text: "Iframe",
                onAction: () => setShowIframeModal(true),
              });
            },
            branding: false,
            menubar: minimal ? false : getMenuBar(),
            plugins: getPlugins(),
            toolbar: getToolbar(),
            elementpath: false,
            extended_valid_elements:
              "iframe[src|frameborder|style|scrolling|class|width|height|name|align]",
            content_style:
              "tbody, tr, td {border-style: inherit !important;}  p { margin: 0 }",
            height: editorHeight,
            resize: false, // Disable manual resize to use our responsive system
          }}
        />
      </EditorContainer>

      <Dialog
        visible={showIframeModal}
        onHide={() => {
          setShowIframeModal(false);
        }}
        footer={null}
        className="export-modal draggable-modal"
        header={"Iframe"}
      >
        <Iframe>
          <div className="row">
            <label>URL</label>
            <Input
              onChange={(e) => {
                setIframe({ ...iframe, url: e.target.value });
              }}
              value={iframe?.url || ""}
            />
          </div>

          <div className="row">
            <label>{t("Width")}</label>
            <Input
              onChange={(e) => {
                setIframe({ ...iframe, width: e.target.value });
              }}
              value={iframe?.width || ""}
            />
          </div>

          <div className="row">
            <label>{t("Height")}</label>
            <Input
              onChange={(e) => {
                setIframe({ ...iframe, height: e.target.value });
              }}
              value={iframe?.height || ""}
            />
          </div>
          <div className="buttons">
            <Button
              type="primary"
              disabled={!iframe?.url}
              onClick={insertIframe}
            >
              {t("Save")}
            </Button>
          </div>
        </Iframe>
      </Dialog>
    </>
  );
};

export { TinyEditor };

const EditorContainer = styled.div<{ height: number | string }>`
  width: 100%;
  height: ${(props: { height: number | string }) => typeof props.height === 'number' ? `${props.height}px` : props.height};
  min-height: 150px;
  max-height: 100%;
  overflow: hidden;
  position: relative;

  /* Ensure the editor fills the container */
  & .hugerte {
    height: 100% !important;
    width: 100% !important;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
  }

  /* Ensure toolbar doesn't overflow */
  & .hugerte-toolbar {
    position: sticky;
    top: 0;
    z-index: 10;
    background: white;
    border-bottom: 1px solid #e8e8e8;
  }

  /* Content area should be scrollable */
  & .hugerte-content-area {
    height: calc(100% - 40px); /* Subtract toolbar height */
    overflow-y: auto;
  }

  /* Status bar adjustments */
  & .hugerte-statusbar {
    position: sticky;
    bottom: 0;
    background: white;
    border-top: 1px solid #e8e8e8;
  }
`;

const Iframe = styled.div`
  padding-bottom: 8px;

  & .row {
    margin-bottom: 8px;
  }
  & label {
    margin-bottom: 2px;
    display: block;
  }
  & .buttons {
    display: flex;
    justify-content: flex-end;
  }
`;
